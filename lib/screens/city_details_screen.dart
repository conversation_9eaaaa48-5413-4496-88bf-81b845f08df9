import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

class CityDetailsScreen extends StatefulWidget {
  final String cityName;

  const CityDetailsScreen({Key? key, required this.cityName}) : super(key: key);

  @override
  State<CityDetailsScreen> createState() => _CityDetailsScreenState();
}

class _CityDetailsScreenState extends State<CityDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dateController = TextEditingController(text: '5/20/2025');

  String _cityDescription = '';
  String _cityLocation = '';
  String _cityUnits = '';
  String _cityDistance = '';

  // Date picker variables
  int _selectedDay = 20;
  int _selectedMonth = 5;
  int _selectedYear = 2025;

  // Get city gradient based on city name
  Gradient _getCityGradient() {
    switch (widget.cityName) {
      case 'جنوب سعد':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF96C5EF), Color(0xFF6E81C6)], // Blue gradient
        );
      case 'جنوب صباح':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF59D2D7), Color(0xFF0BACBC)], // Teal gradient
        );
      case 'مدينة الطيران':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFE6A6D6), Color(0xFFC577B1)], // Pink gradient
        );
      case 'نواف الأحمد':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFB89DD7), Color(0xFF8C6DB0)], // Purple gradient
        );
      case 'مدينة العبادية':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF9A77B), Color(0xFFE67E51)], // Orange gradient
        );
      default:
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFB89DD7),
            Color(0xFF8C6DB0)
          ], // Default purple gradient
        );
    }
  }

  // Get city primary color based on city name
  Color _getCityPrimaryColor() {
    switch (widget.cityName) {
      case 'جنوب سعد':
        return const Color(0xFF6E81C6); // Blue
      case 'جنوب صباح':
        return const Color(0xFF0BACBC); // Teal
      case 'مدينة الطيران':
        return const Color(0xFFC577B1); // Pink
      case 'نواف الأحمد':
        return const Color(0xFF8C6DB0); // Purple
      case 'مدينة العبادية':
        return const Color(0xFFE67E51); // Orange
      default:
        return const Color(0xFF8C6DB0); // Default purple
    }
  }

  @override
  void initState() {
    super.initState();
    _loadCityDetails();
  }

  void _loadCityDetails() {
    // This would be replaced with an actual API call
    switch (widget.cityName) {
      case 'مدينة الطيران':
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
        break;
      case 'جنوب سيناء':
        _cityDescription = 'مدينة جنوب سيناء السكنية';
        _cityLocation = 'تقع في الجهة الشرقية من مدينة الكويت';
        _cityUnits = 'على بعد 95 كيلومتر بعدد 35 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 110 كيلومتر';
        break;
      case 'جنوب صباح':
        _cityDescription = 'مدينة جنوب صباح السكنية';
        _cityLocation = 'تقع في الجهة الجنوبية من مدينة الكويت';
        _cityUnits = 'على بعد 80 كيلومتر بعدد 30 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 95 كيلومتر';
        break;
      case 'مدينة العبادية':
        _cityDescription = 'مدينة الصابرية السكنية';
        _cityLocation = 'تقع في شمال البلاد';
        _cityUnits = 'وتبعد 60 كيلومتر عن مركز المدينة بعدد 55 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 80 كيلومتر';
        break;
      case 'نواف الأحمد':
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
        break;
      default:
        _cityDescription = 'مدينة نواف الأحمد السكنية';
        _cityLocation = 'تقع في الجهة الغربية من مدينة الكويت';
        _cityUnits = 'على بعد 110 كيلومتر بعدد 45 ألف وحدة سكنية';
        _cityDistance = 'ومساحتها 125 كيلومتر';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.arrow_forward_ios, color: Colors.grey),
              onPressed: () => context.go('/home'),
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // City title
                Text(
                  _cityDescription,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: _getCityPrimaryColor(),
                    fontSize: 26,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Zahra',
                  ),
                ),

                // City description
                Text(
                  _cityLocation,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color.fromARGB(178, 0, 0, 0),
                    fontSize: 20,
                    fontFamily: 'Zahra',
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  _cityUnits,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.black54,
                    fontSize: 20,
                    fontFamily: 'Zahra',
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  _cityDistance,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.black54,
                    fontSize: 20,
                    fontFamily: 'Zahra',
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 20),

                // Map placeholder
                Container(
                  height: 180,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.map,
                          size: 80,
                          color: Colors.grey.shade400,
                        ),
                      ),
                      Positioned(
                        bottom: 20,
                        right: 20,
                        child: Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: _getCityPrimaryColor(),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Registration form title
                const Text(
                  'سجل اهتمامك في وحدة بالمدينة',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color.fromARGB(184, 5, 5, 5),
                    fontSize: 18,
                    fontFamily: 'Zahra',
                    fontWeight: FontWeight.w900,
                  ),
                ),
                const SizedBox(height: 10),

                // Progress indicator
                Container(
                  width: 120,
                  height: 4,
                  decoration: BoxDecoration(
                    gradient: _getCityGradient(),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                // Form
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Full Name
                      _buildFormField(
                        'الإسم الثلاثي',
                        Icons.person_outline,
                        TextFormField(
                          controller: _nameController,
                          decoration: _inputDecoration('الإسم الثلاثي'),
                          textAlign: TextAlign.right,
                          textDirection: TextDirection.rtl,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء إدخال الاسم الثلاثي';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 15),

                      // Phone Number
                      _buildFormField(
                        'رقم الهاتف',
                        Icons.phone_outlined,
                        TextFormField(
                          controller: _phoneController,
                          decoration: _inputDecoration('0 0 0 0 0 0 0 0 0 0 0'),
                          keyboardType: TextInputType.phone,
                          textAlign: TextAlign.right,
                          textDirection: TextDirection.rtl,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء إدخال رقم الهاتف';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 15),

                      // Housing Request Year
                      _buildFormField(
                        'سنة الطلب السكني',
                        Icons.calendar_today_outlined,
                        TextFormField(
                          controller: _dateController,
                          decoration: _inputDecoration(''),
                          keyboardType: TextInputType.datetime,
                          textAlign: TextAlign.right,
                          textDirection: TextDirection.rtl,
                          readOnly: true,
                          onTap: () {
                            // Date is selected using the scrollable picker below
                          },
                        ),
                      ),
                      const SizedBox(height: 15),

                      // Date picker
                      _buildDatePicker(),

                      const SizedBox(height: 20),

                      // Submit Button
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: _submitForm,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _getCityPrimaryColor(),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: const Text(
                            'تأكيد',
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormField(String label, IconData icon, Widget field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(
              icon,
              size: 18,
              color: _getCityPrimaryColor(),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: _getCityPrimaryColor(),
                fontSize: 18,
                fontFamily: 'Zahra',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        field,
      ],
    );
  }

  InputDecoration _inputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      hintStyle: TextStyle(
        color: Colors.grey.shade400,
        fontSize: 14,
      ),
      filled: true,
      fillColor: Colors.grey.shade100,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      errorStyle: const TextStyle(
        fontFamily: 'Zahra',
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.red,
      ),
    );
  }

  Widget _buildDatePicker() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Year picker (rightmost for Arabic)
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: const Text(
                    'سنة',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
                Expanded(
                  child: ListWheelScrollView.useDelegate(
                    itemExtent: 40,
                    perspective: 0.005,
                    diameterRatio: 1.2,
                    physics: const FixedExtentScrollPhysics(),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedYear = 2020 + index;
                        _updateDateController();
                      });
                    },
                    childDelegate: ListWheelChildBuilderDelegate(
                      childCount: 11, // 2020 to 2030
                      builder: (context, index) {
                        final year = 2020 + index;
                        final isSelected = year == _selectedYear;
                        return Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? _getCityPrimaryColor()
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            year.toString(),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isSelected ? Colors.white : Colors.grey,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Month picker (middle)
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: const Text(
                    'شهر',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
                Expanded(
                  child: ListWheelScrollView.useDelegate(
                    itemExtent: 40,
                    perspective: 0.005,
                    diameterRatio: 1.2,
                    physics: const FixedExtentScrollPhysics(),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedMonth = index + 1;
                        _updateDateController();
                      });
                    },
                    childDelegate: ListWheelChildBuilderDelegate(
                      childCount: 12,
                      builder: (context, index) {
                        final month = index + 1;
                        final isSelected = month == _selectedMonth;
                        return Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? _getCityPrimaryColor()
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            month.toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isSelected ? Colors.white : Colors.grey,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Day picker (leftmost for Arabic)
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: const Text(
                    'يوم',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
                Expanded(
                  child: ListWheelScrollView.useDelegate(
                    itemExtent: 40,
                    perspective: 0.005,
                    diameterRatio: 1.2,
                    physics: const FixedExtentScrollPhysics(),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedDay = index + 1;
                        _updateDateController();
                      });
                    },
                    childDelegate: ListWheelChildBuilderDelegate(
                      childCount: 31,
                      builder: (context, index) {
                        final day = index + 1;
                        final isSelected = day == _selectedDay;
                        return Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? _getCityPrimaryColor()
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            day.toString(),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isSelected ? Colors.white : Colors.grey,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateDateController() {
    setState(() {
      _dateController.text = '$_selectedMonth/$_selectedDay/$_selectedYear';
    });
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Submit form data
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إرسال طلبك بنجاح')),
      );

      // Navigate back immediately
      context.go('/home');
    }
  }
}
